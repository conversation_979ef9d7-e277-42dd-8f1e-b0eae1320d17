package main

import (
	"context"
	"fmt"
	"strings"

	levelStore "github.com/real-rm/golevelstore"
	golog "github.com/real-rm/golog"
	gomongo "github.com/real-rm/gomongo"
	goresodownload "github.com/real-rm/goresodownload"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// needToProcess determines whether a property needs to be processed based on disk and migration stage
func needToProcess(prop bson.M, disk string) bool {
	src, ok := prop["src"].(string)
	if !ok {
		return false
	}

	// For ca6 stage: Skip if already processed (has phoLH field)
	if disk == "ca6" {
		if prop["phoLH"] != nil {
			return false
		}
	}

	// For ca7 stage: Check if ca7 processing is already done by looking for _old fields
	if disk == "ca7" {
		if src == SRC_TRB || src == SRC_DDF {
			// If pho_old exists, ca7 processing is already done
			if prop["pho_old"] != nil {
				return false
			}
		} else if src == SRC_OTW || src == SRC_CLG {
			// If phoIDs_old exists, ca7 processing is already done
			if prop["phoIDs_old"] != nil {
				return false
			}
		}
	}

	// Common logic for both ca6 and ca7: check original fields
	// For TRB and DDF, check pho field
	if src == SRC_TRB || src == SRC_DDF {
		if pho, ok := prop["pho"]; ok {
			if phoInt, ok := pho.(int32); ok && phoInt > 0 {
				return true
			}
			if phoInt, ok := pho.(int); ok && phoInt > 0 {
				return true
			}
		}
	}

	// For OTW and CLG, check phoIDs field
	if src == SRC_OTW || src == SRC_CLG {
		if phoIDs, ok := prop["phoIDs"]; ok && phoIDs != nil {
			return true
		}
	}

	return false
}

// getPropertiesCursor gets a cursor for streaming properties that need to be processed
func getPropertiesCursor(config Config) (*mongo.Cursor, error) {
	collection := gomongo.Coll("listing", "properties")
	if collection == nil {
		return nil, fmt.Errorf("failed to get properties collection")
	}

	// Build query filter
	filter := bson.M{}
	if config.MT != nil {
		filter["mt"] = bson.M{"$gte": *config.MT}
	}

	// Build projection to include necessary fields
	projection := bson.M{
		"mt":     1,
		"sid":    1,
		"src":    1,
		"ts":     1,
		"board":  1,
		"pho":    1,
		"phoIDs": 1,
		"orgId":  1,
		"phoLH":  1,
		"onD":    1,
	}

	// Build query options with reverse chronological order
	opts := options.Find().
		SetProjection(projection).
		SetSort(bson.M{"ts": -1})

	ctx := context.Background()
	cursor, err := collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to query properties: %w", err)
	}

	golog.Info("Created properties cursor for streaming")
	return cursor, nil
}

// insertImmigrationLog inserts migration log into database
func insertImmigrationLog(result MigrationResult) error {
	var collectionName string
	if result.Disk == "ca6" {
		collectionName = "mls_photo_immigration_ca6"
	} else {
		collectionName = "mls_photo_immigration_ca7"
	}

	collection := gomongo.Coll("rni", collectionName)
	if collection == nil {
		return fmt.Errorf("failed to get %s collection", collectionName)
	}

	ctx := context.Background()
	_, err := collection.InsertOne(ctx, result)
	if err != nil {
		return fmt.Errorf("failed to insert immigration log: %w", err)
	}

	return nil
}

// updateDirStatsFromProp updates directory statistics from property data using phoP path
func updateDirStatsFromProp(prop bson.M, phoP string, fileCount int) error {
	src, ok := prop["src"].(string)
	if !ok {
		return fmt.Errorf("invalid src field")
	}

	sid, ok := prop["sid"].(string)
	if !ok {
		return fmt.Errorf("invalid sid field")
	}

	// Get dirStore based on src type
	var dirStore *levelStore.DirKeyStore
	switch src {
	case SRC_TRB:
		dirStore = storeTRB
	case SRC_DDF:
		dirStore = storeDDF
	case SRC_OTW:
		dirStore = storeOTW
	case SRC_CLG:
		dirStore = storeCLG
	default:
		return fmt.Errorf("unsupported src type: %s", src)
	}

	if dirStore == nil {
		return fmt.Errorf("dirStore not initialized for src: %s", src)
	}

	// Parse L1/L2 from phoP path by splitting on "/"
	// phoP format: "/L1/L2" (e.g., "/100/abc123")
	pathParts := strings.Split(strings.TrimPrefix(phoP, "/"), "/")
	if len(pathParts) < 2 {
		return fmt.Errorf("invalid phoP path format: %s", phoP)
	}

	l1 := pathParts[0]
	l2 := pathParts[1]

	// Update stats: 1 property, fileCount files
	if fileCount != 0 {
		dirStore.AddDirStats(l1, l2, 1, fileCount) // 1 means one property
	}

	// Force l2 to be displayed as string by adding quotes (following the pattern)
	golog.Info("updated dirStore stats", "l1", l1, "l2", fmt.Sprintf("\"%s\"", l2), "fileCount", fileCount, "sid", sid, "phoP", phoP, "src", src)

	return nil
}

// updateDB updates both properties and RNI collections based on ca6/ca7 stage
func updateDB(params DBUpdateParams) error {
	propID, ok := params.Prop["_id"].(string)
	if !ok {
		return fmt.Errorf("invalid prop _id")
	}

	if params.Disk == "ca6" {
		// ca6 stage: Add new phoHL, tnHL, and phoP fields
		if err := updatePropDBCA6(propID, params.PhoHL, params.TnHL, params.PhoP); err != nil {
			return fmt.Errorf("failed to update prop DB for ca6: %w", err)
		}

		// Update RNI collection
		if err := updateRniDBCA6(params.Prop, params.PhoHL, params.TnHL, params.PhoP); err != nil {
			return fmt.Errorf("failed to update rni DB: %w", err)
		}

		// Update directory statistics (only for ca6)
		if err := updateDirStatsFromProp(params.Prop, params.PhoP, len(params.PhoHL)); err != nil {
			golog.Error("Failed to update directory stats", "error", err)
			// Directory stats failure doesn't affect main process
		}

	} else if params.Disk == "ca7" {
		// ca7 stage: Rename old fields to _old
		if err := updatePropDBCA7(propID, params.Prop); err != nil {
			return fmt.Errorf("failed to update prop DB for ca7: %w", err)
		}

		// Update RNI collection for ca7 (rename old fields)
		if err := updateRniDBCA7(params.Prop); err != nil {
			return fmt.Errorf("failed to update rni DB for ca7: %w", err)
		}
	}

	return nil
}

// updatePropDBCA6 updates both properties and merged collections for ca6 stage
func updatePropDBCA6(propID string, phoHL []int32, tnHL int32, phoP string) error {
	updateFields := bson.M{
		"phoHL": phoHL,
		"tnHL":  tnHL,
		"phoP":  phoP,
	}

	update := bson.M{"$set": updateFields}
	return updatePropCollection(propID, update, "ca6")
}

// updateRniDBCA6 updates the corresponding RNI collection with phoP, phoHL, and tnHL
func updateRniDBCA6(prop bson.M, phoHL []int32, tnHL int32, phoP string) error {
	update := bson.M{"$set": bson.M{
		"phoHL": phoHL,
		"tnHL":  tnHL,
		"phoP":  phoP,
	}}
	return updateRniCollection(prop, update, "ca6")
}

// updatePropDBCA7 updates both properties and merged collections for ca7 stage (rename old fields)
func updatePropDBCA7(propID string, prop bson.M) error {
	src, ok := prop["src"].(string)
	if !ok {
		return fmt.Errorf("invalid src field")
	}

	update := bson.M{}

	// Rename fields based on source type using $rename operator
	if src == SRC_TRB || src == SRC_DDF {
		// Rename pho to pho_old
		update["$rename"] = bson.M{"pho": "pho_old"}
	} else if src == SRC_OTW || src == SRC_CLG {
		// Rename phoIDs to phoIDs_old
		update["$rename"] = bson.M{"phoIDs": "phoIDs_old"}
	}

	if len(update) == 0 {
		golog.Info("No fields to rename for ca7", "propID", propID, "src", src)
		return nil
	}

	// Update properties collection only
	return updatePropCollection(propID, update, "ca7")
}

// updateRniDBCA7 updates the RNI collection for ca7 stage (rename old fields)
func updateRniDBCA7(prop bson.M) error {
	src, ok := prop["src"].(string)
	if !ok {
		return fmt.Errorf("invalid src field")
	}

	update := bson.M{}

	// Rename fields based on source type
	if src == SRC_TRB {
		update["$rename"] = bson.M{"picnum": "picnum_old", "pic_num": "pic_num_old", "pho": "pho_old"}
	} else if src == SRC_DDF {
		update["$rename"] = bson.M{"picNum": "picNum_old"}
	} else if src == SRC_OTW || src == SRC_CLG {
		update["$rename"] = bson.M{"phoIDs": "phoIDs_old"}
	}

	return updateRniCollection(prop, update, "ca7")
}

// getBoardMergedTable returns the merged table name for a given src type
func getBoardMergedTable(src string) (string, bool) {
	// Use BoardMergedTable from media_diff_analyzer to avoid duplication
	tableName, exists := goresodownload.BoardMergedTable[src]
	return tableName, exists
}

// updateRniCollection is a common function to update RNI collections
func updateRniCollection(prop bson.M, update bson.M, stage string) error {
	src, ok := prop["src"].(string)
	if !ok {
		return fmt.Errorf("invalid src field")
	}

	var collectionName string
	var queryField string
	var queryValue interface{}

	// Get collection name from BoardMergedTable
	var exists bool
	collectionName, exists = getBoardMergedTable(src)
	if !exists {
		return fmt.Errorf("unsupported src type: %s", src)
	}

	// Determine query parameters based on source type
	switch src {
	case SRC_TRB, SRC_DDF:
		queryField = "_id"
		queryValue = prop["_id"]
	case SRC_OTW, SRC_CLG:
		queryField = "_id"
		queryValue = prop["orgId"]
	default:
		return fmt.Errorf("unsupported src type: %s", src)
	}

	collection := gomongo.Coll("rni", collectionName)
	if collection == nil {
		return fmt.Errorf("failed to get %s collection", collectionName)
	}

	filter := bson.M{queryField: queryValue}

	ctx := context.Background()
	result, err := collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to update rni for %s: %w", stage, err)
	}

	if result.MatchedCount == 0 {
		golog.Info("No rni record found for update", "stage", stage, "collection", collectionName, "queryValue", queryValue)
	} else {
		golog.Info("Updated rni", "stage", stage, "collection", collectionName, "queryValue", queryValue)
	}

	return nil
}

// updatePropCollection is a common function to update properties collection
func updatePropCollection(propID string, update bson.M, stage string) error {
	ctx := context.Background()

	// Update properties collection
	propCollection := gomongo.Coll("listing", "properties")
	if propCollection == nil {
		return fmt.Errorf("failed to get properties collection")
	}

	filter := bson.M{"_id": propID}
	propResult, err := propCollection.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to update prop for %s: %w", stage, err)
	}

	if propResult.MatchedCount == 0 {
		return fmt.Errorf("no prop found with id: %s", propID)
	}

	golog.Info("Updated prop", "stage", stage, "propID", propID)
	return nil
}

// updateMergedCollection is a common function to update merged collections
func updateMergedCollection(propID string, src string, update bson.M, stage string) error {
	// Get merged collection name from BoardMergedTable
	mergedTableName, exists := getBoardMergedTable(src)
	if !exists {
		return fmt.Errorf("unsupported src type: %s", src)
	}

	// Update merged collection using rni database
	mergedCollection := gomongo.Coll("rni", mergedTableName)
	if mergedCollection == nil {
		return fmt.Errorf("failed to get merged collection for src: %s, table: %s", src, mergedTableName)
	}

	ctx := context.Background()
	filter := bson.M{"_id": propID}
	mergedResult, err := mergedCollection.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to update merged for %s: %w", stage, err)
	}

	if mergedResult.MatchedCount == 0 {
		golog.Info("No merged record found for update", "stage", stage, "propID", propID)
	} else {
		golog.Info("Updated merged", "stage", stage, "propID", propID, "src", src, "table", mergedTableName)
	}

	return nil
}
