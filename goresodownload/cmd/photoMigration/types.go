package main

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"
)

// Path constants for old and new directory structures
const (
	OLD_CA6_PATH   = "/mnt/ca6m0/mlsimgs"
	OLD_CA7_PATH   = "/mnt/ca7m0/mlsimgs"
	OLD_LOCAL_PATH = "/mnt/md0/mlsimgs"
	NEW_CA6_PATH   = "/mnt/ca6m0/imgs/MLS"
	NEW_CA7_PATH   = "/mnt/ca7m0/imgs/MLS"
)

// Supported source types
const (
	SRC_TRB = "TRB"
	SRC_DDF = "DDF"
	SRC_OTW = "OTW"
	SRC_CLG = "CLG"
)

// Migration status constants
const (
	STATUS_RENAMED       = "renamed"
	STATUS_COPIED        = "copied"
	STATUS_RENAME_FAILED = "rename_failed"
	STATUS_COPY_FAILED   = "copy_failed"
	STATUS_NOT_FOUND     = "not_found"
)

// Config holds command-line configuration
type Config struct {
	MT     *time.Time // Time filter parameter
	Disk   string     // ca6 or ca7 (both use local path first, ca6 has ca7 fallback)
	DryRun bool       // Whether to run in test mode
}

// MigrationResult represents the result of a migration operation
type MigrationResult struct {
	PropID    string    `json:"prop_id"`
	MT        time.Time `json:"mt"`
	TS        time.Time `json:"ts"`
	SrcFolder string    `json:"src_folder"`
	DstFolder string    `json:"dst_folder"`
	Status    string    `json:"status"`
	ErrMsg    string    `json:"err_msg,omitempty"`
	Disk      string    `json:"disk"`
	CreatedAt time.Time `json:"created_at"`
}

// ImageInfo contains information about a processed image
type ImageInfo struct {
	OriginalPath string
	NewPath      string
	Hash         int32
	Base62       string
}

// PropertyResult represents the result of processing a property
type PropertyResult struct {
	PropID     string
	Processed  bool
	ImageCount int
	Success    int
	Failed     int
	Errors     []error
	PhoP       string   // New phoP path generated during processing
	ImagePaths []string // Original image paths collected
}

// PathBuildParams contains parameters for building file paths
type PathBuildParams struct {
	Prop     bson.M
	Src      string
	Sid      string
	ImageNum int
	ImageID  interface{} // Can be string, int, or other types depending on source
}

// DBUpdateParams contains parameters for database updates
type DBUpdateParams struct {
	Prop     bson.M
	PhoHL    []int32
	TnHL     int32
	NewPaths []string
	Disk     string // ca6 or ca7 to determine update strategy
	PhoP     string // Pre-calculated phoP path
}
