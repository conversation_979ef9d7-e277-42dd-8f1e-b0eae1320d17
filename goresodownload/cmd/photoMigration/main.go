package main

import (
	"flag"
	"fmt"
	"os"
	"time"

	gobase "github.com/real-rm/gobase"
	golog "github.com/real-rm/golog"
	gomongo "github.com/real-rm/gomongo"
)

func init() {
	// Initialize base configuration and logging
	if err := gobase.InitBase(); err != nil {
		golog.Fatalf("Failed to initialize base: %v", err)
	}

	// Initialize MongoDB connection
	if err := gomongo.InitMongoDB(); err != nil {
		golog.Fatalf("Failed to initialize MongoDB: %v", err)
	}
}

func main() {
	// Reset flag set to avoid conflicts with imported packages
	flag.CommandLine = flag.NewFlagSet(os.Args[0], flag.ExitOnError)

	// Define command-line flags
	var (
		mtStr  = flag.String("mt", "", "Time filter in RFC3339 format (e.g., 2025-07-23T15:30:00Z)")
		disk   = flag.String("disk", "", "Disk to process: ca6 or ca7")
		dryRun = flag.Bool("dryrun", false, "Dry run mode - no actual file operations")
		help   = flag.Bool("help", false, "Show help message")
	)

	flag.Parse()

	// Show help if requested
	if *help {
		showHelp()
		return
	}

	// Validate required parameters
	if *disk == "" {
		fmt.Fprintf(os.Stderr, "Error: -disk parameter is required\n")
		showUsage()
		os.Exit(1)
	}

	if *disk != "ca6" && *disk != "ca7" {
		fmt.Fprintf(os.Stderr, "Error: -disk must be either 'ca6' or 'ca7'\n")
		os.Exit(1)
	}

	// Parse time parameter if provided
	var mt *time.Time
	if *mtStr != "" {
		parsedTime, err := time.Parse(time.RFC3339, *mtStr)
		if err != nil {
			fmt.Fprintf(os.Stderr, "Error: Invalid time format for -mt: %v\n", err)
			fmt.Fprintf(os.Stderr, "Expected format: 2025-07-23T15:30:00Z\n")
			os.Exit(1)
		}
		mt = &parsedTime
	}

	// Create configuration
	config := Config{
		MT:     mt,
		Disk:   *disk,
		DryRun: *dryRun,
	}

	// Log startup information
	golog.Info("Starting photo migration",
		"disk", config.Disk,
		"dryRun", config.DryRun,
		"mtFilter", mtStr)

	if config.DryRun {
		golog.Info("DRY RUN MODE: No actual file operations will be performed")
	}

	// Initialize directory key stores
	if err := initDirKeyStore(); err != nil {
		golog.Fatalf("Failed to initialize directory key store: %v", err)
	}

	// Execute migration
	if err := migrateDisk(config.Disk, config); err != nil {
		golog.Fatalf("Migration failed: %v", err)
	}

	golog.Info("Photo migration completed successfully")
}

// showHelp displays detailed help information
func showHelp() {
	fmt.Println("Photo Migration Tool")
	fmt.Println("====================")
	fmt.Println()
	fmt.Println("This tool migrates photos from old directory structure to new levelstore-based structure.")
	fmt.Println()
	fmt.Println("USAGE:")
	showUsage()
	fmt.Println()
	fmt.Println("PARAMETERS:")
	fmt.Println("  -disk string")
	fmt.Println("        Disk to process: 'ca6' or 'ca7' (required)")
	fmt.Println("        Production environment should run ca6 first, then ca7")
	fmt.Println()
	fmt.Println("  -mt string")
	fmt.Println("        Time filter in RFC3339 format (optional)")
	fmt.Println("        Only process records with mt >= specified time")
	fmt.Println("        Example: -mt \"2025-07-23T15:30:00Z\"")
	fmt.Println()
	fmt.Println("  -dryrun")
	fmt.Println("        Dry run mode - no actual file operations (optional)")
	fmt.Println("        Use this for testing and validation")
	fmt.Println()
	fmt.Println("  -help")
	fmt.Println("        Show this help message")
	fmt.Println()
	fmt.Println("EXAMPLES:")
	fmt.Println("  # Migrate all photos on ca6 disk")
	fmt.Println("  ./photoMigration -disk ca6")
	fmt.Println()
	fmt.Println("  # Migrate photos modified after specific time on ca7 disk")
	fmt.Println("  ./photoMigration -disk ca7 -mt \"2025-07-23T15:30:00Z\"")
	fmt.Println()
	fmt.Println("  # Dry run to test migration on ca6 disk")
	fmt.Println("  ./photoMigration -disk ca6 -dryrun")
	fmt.Println()
	fmt.Println("MIGRATION PROCESS:")
	fmt.Println("  1. Query properties from MongoDB with optional time filter")
	fmt.Println("  2. Process each property in reverse chronological order")
	fmt.Println("  3. Skip properties that already have phoLH field (already migrated)")
	fmt.Println("  4. For each image in the property:")
	fmt.Println("     - Build original path based on src type (TRB/DDF/OTW/CLG)")
	fmt.Println("     - Generate new path using levelstore L1/L2 structure")
	fmt.Println("     - Try to rename file (move within same disk)")
	fmt.Println("     - If rename fails and disk=ca6, try to copy from ca7")
	fmt.Println("     - Generate thumbnail for first image")
	fmt.Println("  5. Update database with new phoHL and tnHL fields")
	fmt.Println("  6. Log all operations and errors")
	fmt.Println()
	fmt.Println("SUPPORTED SOURCE TYPES:")
	fmt.Println("  - TRB: Toronto Real Estate Board")
	fmt.Println("  - DDF: Data Distribution Facility")
	fmt.Println("  - OTW: Ottawa Real Estate Board")
	fmt.Println("  - CLG: Calgary Real Estate Board")
	fmt.Println()
	fmt.Println("OUTPUT:")
	fmt.Println("  - Detailed logs in configured log files")
	fmt.Println("  - Migration results in photo_immigration_ca6/ca7 MongoDB collections")
	fmt.Println("  - Updated phoHL and tnHL fields in properties and RNI collections")
}

// showUsage displays basic usage information
func showUsage() {
	fmt.Println("  ./photoMigration -disk <ca6|ca7> [-mt <time>] [-dryrun] [-help]")
}
